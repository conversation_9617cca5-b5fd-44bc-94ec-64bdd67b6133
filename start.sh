#!/bin/bash

# PHP + Keycloak + Traefik Demo 启动脚本
# 作者: Demo Project
# 版本: 1.0

set -e

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 打印带颜色的消息
print_message() {
    echo -e "${2}${1}${NC}"
}

print_header() {
    echo
    print_message "================================================" $BLUE
    print_message "$1" $BLUE
    print_message "================================================" $BLUE
    echo
}

print_success() {
    print_message "✓ $1" $GREEN
}

print_warning() {
    print_message "⚠ $1" $YELLOW
}

print_error() {
    print_message "✗ $1" $RED
}

# 检查依赖
check_dependencies() {
    print_header "检查系统依赖"
    
    if ! command -v docker &> /dev/null; then
        print_error "Docker 未安装，请先安装 Docker"
        exit 1
    fi
    print_success "Docker 已安装"
    
    if ! command -v docker-compose &> /dev/null && ! docker compose version &> /dev/null; then
        print_error "Docker Compose 未安装，请先安装 Docker Compose"
        exit 1
    fi
    print_success "Docker Compose 已安装"
    
    # 检查 Docker 是否运行
    if ! docker info &> /dev/null; then
        print_error "Docker 服务未运行，请启动 Docker"
        exit 1
    fi
    print_success "Docker 服务正在运行"
}

# 清理旧容器和网络
cleanup() {
    print_header "清理旧容器和网络"
    
    # 停止并删除容器
    if docker-compose ps -q 2>/dev/null | grep -q .; then
        print_message "停止现有容器..." $YELLOW
        docker-compose down --remove-orphans
    fi
    
    # 清理未使用的网络
    docker network prune -f &> /dev/null || true
    
    print_success "清理完成"
}

# 构建和启动服务
start_services() {
    print_header "构建和启动服务"
    
    # 安装 PHP 依赖
    print_message "安装 PHP 依赖..." $YELLOW
    if [ ! -d "app/vendor" ]; then
        docker run --rm -v "$(pwd)/app:/app" -w /app composer:latest install --no-dev --optimize-autoloader
    fi
    print_success "PHP 依赖安装完成"
    
    # 构建并启动服务
    print_message "构建 Docker 镜像..." $YELLOW
    docker-compose build --no-cache
    
    print_message "启动服务..." $YELLOW
    docker-compose up -d
    
    print_success "所有服务已启动"
}

# 等待服务就绪
wait_for_services() {
    print_header "等待服务就绪"
    
    # 等待 MySQL
    print_message "等待 MySQL 数据库..." $YELLOW
    timeout=60
    while ! docker-compose exec -T mysql mysqladmin ping -h localhost --silent 2>/dev/null; do
        sleep 2
        timeout=$((timeout - 2))
        if [ $timeout -le 0 ]; then
            print_error "MySQL 启动超时"
            exit 1
        fi
    done
    print_success "MySQL 数据库已就绪"
    
    # 等待 Keycloak
    print_message "等待 Keycloak 服务..." $YELLOW
    timeout=120
    while ! curl -s http://keycloak.localhost/health/ready &>/dev/null; do
        sleep 5
        timeout=$((timeout - 5))
        if [ $timeout -le 0 ]; then
            print_error "Keycloak 启动超时"
            exit 1
        fi
    done
    print_success "Keycloak 服务已就绪"
    
    # 等待 PHP 应用
    print_message "等待 PHP 应用..." $YELLOW
    timeout=60
    while ! curl -s http://app.localhost &>/dev/null; do
        sleep 2
        timeout=$((timeout - 2))
        if [ $timeout -le 0 ]; then
            print_error "PHP 应用启动超时"
            exit 1
        fi
    done
    print_success "PHP 应用已就绪"
}

# 导入 Keycloak 配置
import_keycloak_config() {
    print_header "配置 Keycloak"
    
    print_message "导入 Realm 配置..." $YELLOW
    
    # 等待一段时间确保 Keycloak 完全启动
    sleep 10
    
    # 检查 realm 是否已存在
    if docker-compose exec -T keycloak /opt/keycloak/bin/kcadm.sh get realms/php-demo --server http://localhost:8080 --realm master --user admin --password admin123 &>/dev/null; then
        print_warning "Realm 'php-demo' 已存在，跳过导入"
    else
        # 导入 realm 配置
        if docker-compose exec -T keycloak /opt/keycloak/bin/kcadm.sh create realms -f /opt/keycloak/data/import/realm-export.json --server http://localhost:8080 --realm master --user admin --password admin123; then
            print_success "Realm 配置导入成功"
        else
            print_warning "Realm 配置导入失败，请手动配置"
        fi
    fi
}

# 显示访问信息
show_access_info() {
    print_header "部署完成！"
    
    echo
    print_message "🎉 所有服务已成功启动！" $GREEN
    echo
    print_message "📱 应用访问地址：" $BLUE
    echo "   • 主应用：      http://app.localhost"
    echo "   • Keycloak：    http://keycloak.localhost"
    echo "   • Traefik：     http://traefik.localhost"
    echo
    print_message "🔐 默认账户信息：" $BLUE
    echo "   • Keycloak 管理员：admin / admin123"
    echo "   • 测试用户：       user / user123"
    echo
    print_message "📚 快速开始：" $BLUE
    echo "   1. 访问 http://app.localhost"
    echo "   2. 点击 '注册账户' 创建新用户"
    echo "   3. 或使用测试账户 'user / user123' 登录"
    echo "   4. 访问 http://keycloak.localhost 管理用户"
    echo
    print_message "🛠 管理工具：" $BLUE
    echo "   • 查看日志：docker-compose logs -f"
    echo "   • 停止服务：docker-compose down"
    echo "   • 重启服务：docker-compose restart"
    echo
    print_warning "注意：请确保在 /etc/hosts 中添加以下条目："
    echo "127.0.0.1 app.localhost"
    echo "127.0.0.1 keycloak.localhost"
    echo "127.0.0.1 traefik.localhost"
    echo
}

# 主函数
main() {
    print_header "PHP + Keycloak + Traefik Demo 部署脚本"
    
    check_dependencies
    cleanup
    start_services
    wait_for_services
    import_keycloak_config
    show_access_info
}

# 错误处理
trap 'print_error "脚本执行失败！"; exit 1' ERR

# 执行主函数
main "$@"
