{"id": "php-demo", "realm": "php-demo", "displayName": "PHP Demo Realm", "enabled": true, "registrationAllowed": true, "registrationEmailAsUsername": true, "rememberMe": true, "verifyEmail": true, "loginWithEmailAllowed": true, "duplicateEmailsAllowed": false, "resetPasswordAllowed": true, "editUsernameAllowed": false, "bruteForceProtected": true, "permanentLockout": false, "maxFailureWaitSeconds": 900, "minimumQuickLoginWaitSeconds": 60, "waitIncrementSeconds": 60, "quickLoginCheckMilliSeconds": 1000, "maxDeltaTimeSeconds": 43200, "failureFactor": 30, "defaultRoles": ["default-roles-php-demo"], "requiredCredentials": ["password"], "passwordPolicy": "length(8)", "otpPolicyType": "totp", "otpPolicyAlgorithm": "HmacSHA1", "otpPolicyInitialCounter": 0, "otpPolicyDigits": 6, "otpPolicyLookAheadWindow": 1, "otpPolicyPeriod": 30, "clients": [{"id": "php-app-client", "clientId": "php-app", "name": "PHP Application", "description": "PHP Demo Application Client", "enabled": true, "clientAuthenticatorType": "client-secret", "secret": "php-app-secret-key-123", "redirectUris": ["http://app.localhost/*", "http://localhost:8000/*"], "webOrigins": ["http://app.localhost", "http://localhost:8000"], "protocol": "openid-connect", "publicClient": false, "standardFlowEnabled": true, "implicitFlowEnabled": false, "directAccessGrantsEnabled": true, "serviceAccountsEnabled": false, "authorizationServicesEnabled": false, "fullScopeAllowed": true, "nodeReRegistrationTimeout": -1, "defaultClientScopes": ["web-origins", "role_list", "profile", "roles", "email"], "optionalClientScopes": ["address", "phone", "offline_access", "microprofile-jwt"]}], "users": [{"id": "demo-user-1", "username": "user", "enabled": true, "emailVerified": true, "firstName": "Demo", "lastName": "User", "email": "<EMAIL>", "credentials": [{"type": "password", "value": "user123", "temporary": false}], "realmRoles": ["default-roles-php-demo"], "clientRoles": {}, "groups": []}], "roles": {"realm": [{"id": "default-roles-php-demo", "name": "default-roles-php-demo", "description": "Default roles for php-demo realm", "composite": true, "composites": {"realm": ["offline_access", "uma_authorization"], "client": {"account": ["view-profile", "manage-account"]}}}, {"id": "admin", "name": "admin", "description": "Administrator role"}, {"id": "user", "name": "user", "description": "Regular user role"}]}, "groups": [], "defaultDefaultClientScopes": ["role_list", "profile", "email", "web-origins"], "defaultOptionalClientScopes": ["offline_access", "address", "phone", "microprofile-jwt"], "smtpServer": {"host": "localhost", "port": "587", "from": "<EMAIL>", "fromDisplayName": "PHP Demo App", "ssl": "false", "starttls": "true"}}