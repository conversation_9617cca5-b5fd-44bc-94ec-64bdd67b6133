# PHP + Keycloak + Traefik Demo

这是一个完整的 PHP 项目演示，集成了 Keycloak 身份认证和 Traefik 反向代理。

## 功能特性

- 🔐 用户登录/注册（通过 Keycloak）
- 📧 邮箱验证
- 👥 用户管理
- 🔄 反向代理（Traefik）
- 🐳 Docker 容器化部署

## 项目结构

```
.
├── app/                    # PHP 应用代码
│   ├── public/            # Web 根目录
│   ├── src/               # 源代码
│   └── config/            # 配置文件
├── docker/                # Docker 相关文件
│   ├── php/              # PHP Dockerfile
│   └── keycloak/         # Keycloak 配置
├── traefik/              # Traefik 配置
├── docker-compose.yml    # Docker Compose 配置
└── start.sh             # 启动脚本
```

## 快速开始

### 前置要求
- Docker 20.10+
- Docker Compose 2.0+
- 至少 4GB 可用内存

### 安装步骤

1. **配置 hosts 文件**

   在 `/etc/hosts` (Linux/Mac) 或 `C:\Windows\System32\drivers\etc\hosts` (Windows) 中添加：
   ```
   127.0.0.1 app.localhost
   127.0.0.1 keycloak.localhost
   127.0.0.1 traefik.localhost
   ```

2. **运行启动脚本**
   ```bash
   ./start.sh
   ```

3. **等待服务启动完成**（大约 2-3 分钟）

4. **访问应用**
   - 主应用：http://app.localhost
   - Keycloak 管理：http://keycloak.localhost
   - Traefik 仪表板：http://traefik.localhost

## 默认账户

- Keycloak 管理员：admin / admin123
- 测试用户：user / user123

## 服务端口

- 应用：http://app.localhost (通过 Traefik)
- Keycloak：http://keycloak.localhost (通过 Traefik)
- Traefik 仪表板：http://traefik.localhost

## 使用说明

### 用户注册和登录
1. 访问 http://app.localhost
2. 点击"注册账户"创建新用户
3. 或使用测试账户登录：`user / user123`
4. 邮箱验证功能已配置（开发环境可跳过）

### 管理功能
- **用户管理**：访问 http://keycloak.localhost/admin
- **系统监控**：访问 http://traefik.localhost
- **应用管理**：登录后访问管理面板

### 常用命令
```bash
# 查看服务状态
docker-compose ps

# 查看日志
docker-compose logs -f

# 停止服务
docker-compose down

# 重启服务
docker-compose restart
```

## 注意事项

- 本项目仅用于开发和演示
- 生产环境请修改默认密码和密钥
- 确保 Docker 和 Docker Compose 已安装
- 详细安装说明请参考 [SETUP.md](SETUP.md)
