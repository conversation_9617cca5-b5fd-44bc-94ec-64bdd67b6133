# PHP + Keycloak + <PERSON><PERSON><PERSON><PERSON> Demo 安装指南

## 系统要求

- Docker 20.10+
- Docker Compose 2.0+
- 至少 4GB 可用内存
- 至少 2GB 可用磁盘空间

## 快速安装

### 1. 克隆项目

```bash
git clone <repository-url>
cd keyclock3
```

### 2. 配置 hosts 文件

在 `/etc/hosts` (Linux/Mac) 或 `C:\Windows\System32\drivers\etc\hosts` (Windows) 中添加：

```
127.0.0.1 app.localhost
127.0.0.1 keycloak.localhost
127.0.0.1 traefik.localhost
```

### 3. 运行启动脚本

```bash
./start.sh
```

启动脚本会自动：
- 检查系统依赖
- 安装 PHP 依赖
- 构建 Docker 镜像
- 启动所有服务
- 配置 Keycloak
- 显示访问信息

## 手动安装

如果自动脚本失败，可以手动执行以下步骤：

### 1. 安装 PHP 依赖

```bash
cd app
docker run --rm -v "$(pwd):/app" -w /app composer:latest install
cd ..
```

### 2. 构建和启动服务

```bash
docker-compose build
docker-compose up -d
```

### 3. 等待服务启动

```bash
# 检查服务状态
docker-compose ps

# 查看日志
docker-compose logs -f
```

### 4. 导入 Keycloak 配置

```bash
# 等待 Keycloak 完全启动
sleep 30

# 导入 realm 配置
docker-compose exec keycloak /opt/keycloak/bin/kcadm.sh create realms \
  -f /opt/keycloak/data/import/realm-export.json \
  --server http://localhost:8080 \
  --realm master \
  --user admin \
  --password admin123
```

## 访问应用

- **主应用**: http://app.localhost
- **Keycloak 管理**: http://keycloak.localhost
- **Traefik 仪表板**: http://traefik.localhost

## 默认账户

### Keycloak 管理员
- 用户名: `admin`
- 密码: `admin123`

### 测试用户
- 用户名: `user`
- 密码: `user123`

## 常用命令

### 查看服务状态
```bash
docker-compose ps
```

### 查看日志
```bash
# 查看所有服务日志
docker-compose logs -f

# 查看特定服务日志
docker-compose logs -f php-app
docker-compose logs -f keycloak
docker-compose logs -f traefik
```

### 重启服务
```bash
# 重启所有服务
docker-compose restart

# 重启特定服务
docker-compose restart php-app
```

### 停止服务
```bash
docker-compose down
```

### 完全清理
```bash
docker-compose down -v --remove-orphans
docker system prune -f
```

## 故障排除

### 1. 端口冲突

如果遇到端口冲突，检查以下端口是否被占用：
- 80 (Traefik)
- 8080 (Traefik 仪表板)

```bash
# 检查端口占用
lsof -i :80
lsof -i :8080
```

### 2. 域名解析问题

确保 hosts 文件配置正确：

```bash
# 测试域名解析
ping app.localhost
ping keycloak.localhost
ping traefik.localhost
```

### 3. Keycloak 启动失败

检查 MySQL 是否正常启动：

```bash
docker-compose logs mysql
docker-compose exec mysql mysql -u root -prootpassword -e "SHOW DATABASES;"
```

### 4. PHP 应用无法访问

检查 PHP 容器状态和日志：

```bash
docker-compose logs php-app
docker-compose exec php-app php -v
```

### 5. 权限问题

确保文件权限正确：

```bash
# 修复权限
sudo chown -R $USER:$USER .
chmod +x start.sh
```

## 开发模式

### 启用调试模式

编辑 `app/.env` 文件：

```env
APP_DEBUG=true
```

### 实时代码更新

PHP 代码修改会立即生效，无需重启容器。

### 查看详细日志

```bash
# 启用详细日志
docker-compose logs -f --tail=100
```

## 生产部署注意事项

1. **修改默认密码**
   - Keycloak 管理员密码
   - MySQL root 密码
   - 客户端密钥

2. **启用 HTTPS**
   - 配置 SSL 证书
   - 更新 Traefik 配置

3. **数据持久化**
   - 配置数据卷备份
   - 设置数据库备份策略

4. **安全配置**
   - 限制网络访问
   - 配置防火墙规则
   - 启用安全头

5. **监控和日志**
   - 配置日志收集
   - 设置监控告警
   - 性能优化

## 支持

如果遇到问题，请检查：

1. Docker 和 Docker Compose 版本
2. 系统资源使用情况
3. 网络连接状态
4. 日志文件中的错误信息

更多帮助请参考项目文档或提交 Issue。
