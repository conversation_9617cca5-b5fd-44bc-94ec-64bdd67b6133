// PHP Demo App JavaScript

document.addEventListener('DOMContentLoaded', function() {
    // 初始化工具提示
    var tooltipTriggerList = [].slice.call(document.querySelectorAll('[data-bs-toggle="tooltip"]'));
    var tooltipList = tooltipTriggerList.map(function (tooltipTriggerEl) {
        return new bootstrap.Tooltip(tooltipTriggerEl);
    });

    // 初始化弹出框
    var popoverTriggerList = [].slice.call(document.querySelectorAll('[data-bs-toggle="popover"]'));
    var popoverList = popoverTriggerList.map(function (popoverTriggerEl) {
        return new bootstrap.Popover(popoverTriggerEl);
    });

    // 自动隐藏警告消息
    setTimeout(function() {
        var alerts = document.querySelectorAll('.alert:not(.alert-permanent)');
        alerts.forEach(function(alert) {
            var bsAlert = new bootstrap.Alert(alert);
            bsAlert.close();
        });
    }, 5000);

    // 添加加载状态到按钮
    var buttons = document.querySelectorAll('.btn[data-loading-text]');
    buttons.forEach(function(button) {
        button.addEventListener('click', function() {
            var originalText = this.innerHTML;
            var loadingText = this.getAttribute('data-loading-text');
            
            this.innerHTML = '<span class="spinner-border spinner-border-sm me-2" role="status"></span>' + loadingText;
            this.disabled = true;
            
            // 模拟加载完成（实际应用中应该在请求完成后恢复）
            setTimeout(function() {
                button.innerHTML = originalText;
                button.disabled = false;
            }, 2000);
        });
    });

    // 复制到剪贴板功能
    window.copyToClipboard = function(text) {
        navigator.clipboard.writeText(text).then(function() {
            showToast('已复制到剪贴板', 'success');
        }).catch(function() {
            showToast('复制失败', 'error');
        });
    };

    // 显示 Toast 消息
    window.showToast = function(message, type = 'info') {
        var toastContainer = document.getElementById('toast-container');
        if (!toastContainer) {
            toastContainer = document.createElement('div');
            toastContainer.id = 'toast-container';
            toastContainer.className = 'toast-container position-fixed top-0 end-0 p-3';
            toastContainer.style.zIndex = '1055';
            document.body.appendChild(toastContainer);
        }

        var toastId = 'toast-' + Date.now();
        var toastHtml = `
            <div id="${toastId}" class="toast" role="alert" aria-live="assertive" aria-atomic="true">
                <div class="toast-header">
                    <i class="fas fa-${getToastIcon(type)} text-${getToastColor(type)} me-2"></i>
                    <strong class="me-auto">通知</strong>
                    <button type="button" class="btn-close" data-bs-dismiss="toast"></button>
                </div>
                <div class="toast-body">
                    ${message}
                </div>
            </div>
        `;

        toastContainer.insertAdjacentHTML('beforeend', toastHtml);
        var toastElement = document.getElementById(toastId);
        var toast = new bootstrap.Toast(toastElement);
        toast.show();

        // 自动移除 DOM 元素
        toastElement.addEventListener('hidden.bs.toast', function() {
            toastElement.remove();
        });
    };

    function getToastIcon(type) {
        switch(type) {
            case 'success': return 'check-circle';
            case 'error': return 'exclamation-triangle';
            case 'warning': return 'exclamation-circle';
            default: return 'info-circle';
        }
    }

    function getToastColor(type) {
        switch(type) {
            case 'success': return 'success';
            case 'error': return 'danger';
            case 'warning': return 'warning';
            default: return 'info';
        }
    }

    // 检查服务状态
    window.checkServiceStatus = function() {
        var services = [
            { name: 'Keycloak', url: 'http://keycloak.localhost' },
            { name: 'Traefik', url: 'http://traefik.localhost' }
        ];

        services.forEach(function(service) {
            fetch(service.url, { mode: 'no-cors' })
                .then(function() {
                    updateServiceStatus(service.name, 'online');
                })
                .catch(function() {
                    updateServiceStatus(service.name, 'offline');
                });
        });
    };

    function updateServiceStatus(serviceName, status) {
        var indicators = document.querySelectorAll(`[data-service="${serviceName}"]`);
        indicators.forEach(function(indicator) {
            indicator.className = `status-indicator status-${status}`;
        });
    }

    // 页面加载完成后检查服务状态
    if (document.querySelector('.status-indicator')) {
        checkServiceStatus();
        // 每30秒检查一次
        setInterval(checkServiceStatus, 30000);
    }
});

// 全局错误处理
window.addEventListener('error', function(e) {
    console.error('JavaScript Error:', e.error);
    if (typeof showToast === 'function') {
        showToast('发生了一个错误，请刷新页面重试', 'error');
    }
});

// 网络状态检测
window.addEventListener('online', function() {
    if (typeof showToast === 'function') {
        showToast('网络连接已恢复', 'success');
    }
});

window.addEventListener('offline', function() {
    if (typeof showToast === 'function') {
        showToast('网络连接已断开', 'warning');
    }
});
