/* 自定义样式 */
body {
    background-color: #f8f9fa;
    font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
}

.navbar-brand {
    font-weight: bold;
}

.card {
    border: none;
    border-radius: 10px;
    transition: transform 0.2s ease-in-out, box-shadow 0.2s ease-in-out;
}

.card:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
}

.btn {
    border-radius: 6px;
    font-weight: 500;
    transition: all 0.2s ease-in-out;
}

.btn:hover {
    transform: translateY(-1px);
}

.avatar-container {
    position: relative;
    display: inline-block;
}

.avatar-container::after {
    content: '';
    position: absolute;
    bottom: 10px;
    right: 10px;
    width: 20px;
    height: 20px;
    background-color: #28a745;
    border: 3px solid white;
    border-radius: 50%;
}

.spinner-border {
    width: 3rem;
    height: 3rem;
}

/* 响应式调整 */
@media (max-width: 768px) {
    .container {
        padding-left: 15px;
        padding-right: 15px;
    }
    
    .card-body {
        padding: 1rem;
    }
}

/* 动画效果 */
@keyframes fadeIn {
    from {
        opacity: 0;
        transform: translateY(20px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

.card {
    animation: fadeIn 0.5s ease-out;
}

/* 状态指示器 */
.status-indicator {
    display: inline-block;
    width: 10px;
    height: 10px;
    border-radius: 50%;
    margin-right: 8px;
}

.status-online {
    background-color: #28a745;
}

.status-offline {
    background-color: #dc3545;
}

.status-warning {
    background-color: #ffc107;
}

/* 代码块样式 */
pre code {
    font-size: 0.875rem;
    line-height: 1.4;
}

/* 表格样式 */
.table-borderless td {
    border: none;
    padding: 0.5rem 0;
}

/* 自定义徽章 */
.badge {
    font-size: 0.75rem;
    padding: 0.375rem 0.75rem;
}

/* 页脚样式 */
footer {
    margin-top: auto;
}

footer a {
    color: #6c757d;
    text-decoration: none;
}

footer a:hover {
    color: #495057;
    text-decoration: underline;
}
