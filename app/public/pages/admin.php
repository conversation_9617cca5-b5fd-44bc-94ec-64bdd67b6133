<?php
$title = '管理面板 - PHP Demo App';

// 检查用户是否已登录
if (!$session->isAuthenticated()) {
    header('Location: /login');
    exit;
}

$user = $session->getUser();

ob_start();
?>

<div class="row">
    <div class="col-12">
        <div class="card shadow-sm">
            <div class="card-header">
                <h4 class="mb-0">
                    <i class="fas fa-cogs me-2"></i>
                    管理面板
                </h4>
            </div>
            <div class="card-body">
                <div class="alert alert-info" role="alert">
                    <h5 class="alert-heading">
                        <i class="fas fa-info-circle me-2"></i>
                        管理功能
                    </h5>
                    <p>这里是管理面板，您可以访问各种管理功能。在生产环境中，这些功能应该基于用户角色进行权限控制。</p>
                </div>
                
                <div class="row">
                    <!-- Keycloak 管理 -->
                    <div class="col-md-6 col-lg-4 mb-4">
                        <div class="card h-100">
                            <div class="card-body text-center">
                                <i class="fas fa-users-cog fa-3x text-primary mb-3"></i>
                                <h5 class="card-title">用户管理</h5>
                                <p class="card-text">管理用户账户、角色和权限</p>
                                <a href="http://keycloak.localhost/admin" 
                                   target="_blank" 
                                   class="btn btn-primary">
                                    <i class="fas fa-external-link-alt me-2"></i>
                                    打开 Keycloak
                                </a>
                            </div>
                        </div>
                    </div>
                    
                    <!-- Traefik 仪表板 -->
                    <div class="col-md-6 col-lg-4 mb-4">
                        <div class="card h-100">
                            <div class="card-body text-center">
                                <i class="fas fa-network-wired fa-3x text-success mb-3"></i>
                                <h5 class="card-title">网络监控</h5>
                                <p class="card-text">查看 Traefik 路由和服务状态</p>
                                <a href="http://traefik.localhost" 
                                   target="_blank" 
                                   class="btn btn-success">
                                    <i class="fas fa-external-link-alt me-2"></i>
                                    打开 Traefik
                                </a>
                            </div>
                        </div>
                    </div>
                    
                    <!-- 系统信息 -->
                    <div class="col-md-6 col-lg-4 mb-4">
                        <div class="card h-100">
                            <div class="card-body text-center">
                                <i class="fas fa-server fa-3x text-info mb-3"></i>
                                <h5 class="card-title">系统信息</h5>
                                <p class="card-text">查看系统状态和配置信息</p>
                                <button class="btn btn-info" onclick="toggleSystemInfo()">
                                    <i class="fas fa-info-circle me-2"></i>
                                    查看详情
                                </button>
                            </div>
                        </div>
                    </div>
                </div>
                
                <!-- 系统信息详情 -->
                <div id="systemInfo" class="mt-4" style="display: none;">
                    <div class="card">
                        <div class="card-header">
                            <h6 class="mb-0">系统信息</h6>
                        </div>
                        <div class="card-body">
                            <div class="row">
                                <div class="col-md-6">
                                    <h6>PHP 信息</h6>
                                    <ul class="list-unstyled">
                                        <li><strong>PHP 版本：</strong> <?= PHP_VERSION ?></li>
                                        <li><strong>服务器软件：</strong> <?= $_SERVER['SERVER_SOFTWARE'] ?? 'Unknown' ?></li>
                                        <li><strong>文档根目录：</strong> <?= $_SERVER['DOCUMENT_ROOT'] ?? 'Unknown' ?></li>
                                    </ul>
                                </div>
                                <div class="col-md-6">
                                    <h6>环境变量</h6>
                                    <ul class="list-unstyled">
                                        <li><strong>Keycloak URL：</strong> <?= $_ENV['KEYCLOAK_BASE_URL'] ?? 'Not set' ?></li>
                                        <li><strong>应用 URL：</strong> <?= $_ENV['APP_URL'] ?? 'Not set' ?></li>
                                        <li><strong>调试模式：</strong> <?= ($_ENV['APP_DEBUG'] ?? false) ? '开启' : '关闭' ?></li>
                                    </ul>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                
                <!-- 快速操作 -->
                <div class="mt-4">
                    <h6>快速操作</h6>
                    <div class="btn-group" role="group">
                        <a href="/profile" class="btn btn-outline-primary">
                            <i class="fas fa-user me-2"></i>
                            个人资料
                        </a>
                        <a href="/" class="btn btn-outline-secondary">
                            <i class="fas fa-home me-2"></i>
                            返回首页
                        </a>
                        <a href="/logout" class="btn btn-outline-danger">
                            <i class="fas fa-sign-out-alt me-2"></i>
                            退出登录
                        </a>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<script>
function toggleSystemInfo() {
    const systemInfo = document.getElementById('systemInfo');
    if (systemInfo.style.display === 'none') {
        systemInfo.style.display = 'block';
    } else {
        systemInfo.style.display = 'none';
    }
}
</script>

<?php
$content = ob_get_clean();
include __DIR__ . '/layout.php';
?>
