<?php
$title = '个人资料 - PHP Demo App';

// 检查用户是否已登录
if (!$session->isAuthenticated()) {
    header('Location: /login');
    exit;
}

$user = $session->getUser();

ob_start();
?>

<div class="row">
    <div class="col-lg-8 mx-auto">
        <div class="card shadow-sm">
            <div class="card-header">
                <h4 class="mb-0">
                    <i class="fas fa-user me-2"></i>
                    个人资料
                </h4>
            </div>
            <div class="card-body">
                <div class="row">
                    <div class="col-md-4 text-center mb-4">
                        <div class="avatar-container">
                            <?php if (isset($user['picture'])): ?>
                                <img src="<?= htmlspecialchars($user['picture']) ?>" 
                                     alt="头像" 
                                     class="rounded-circle img-fluid"
                                     style="width: 150px; height: 150px; object-fit: cover;">
                            <?php else: ?>
                                <div class="bg-primary rounded-circle d-flex align-items-center justify-content-center mx-auto"
                                     style="width: 150px; height: 150px;">
                                    <i class="fas fa-user fa-4x text-white"></i>
                                </div>
                            <?php endif; ?>
                        </div>
                        <h5 class="mt-3"><?= htmlspecialchars($user['name'] ?? $user['preferred_username'] ?? '用户') ?></h5>
                        <p class="text-muted"><?= htmlspecialchars($user['email'] ?? '') ?></p>
                    </div>
                    
                    <div class="col-md-8">
                        <h6>基本信息</h6>
                        <table class="table table-borderless">
                            <tbody>
                                <tr>
                                    <td><strong>用户名：</strong></td>
                                    <td><?= htmlspecialchars($user['preferred_username'] ?? 'N/A') ?></td>
                                </tr>
                                <tr>
                                    <td><strong>姓名：</strong></td>
                                    <td><?= htmlspecialchars($user['name'] ?? 'N/A') ?></td>
                                </tr>
                                <tr>
                                    <td><strong>名字：</strong></td>
                                    <td><?= htmlspecialchars($user['given_name'] ?? 'N/A') ?></td>
                                </tr>
                                <tr>
                                    <td><strong>姓氏：</strong></td>
                                    <td><?= htmlspecialchars($user['family_name'] ?? 'N/A') ?></td>
                                </tr>
                                <tr>
                                    <td><strong>邮箱：</strong></td>
                                    <td>
                                        <?= htmlspecialchars($user['email'] ?? 'N/A') ?>
                                        <?php if (isset($user['email_verified']) && $user['email_verified']): ?>
                                            <span class="badge bg-success ms-2">已验证</span>
                                        <?php else: ?>
                                            <span class="badge bg-warning ms-2">未验证</span>
                                        <?php endif; ?>
                                    </td>
                                </tr>
                                <tr>
                                    <td><strong>用户 ID：</strong></td>
                                    <td><code><?= htmlspecialchars($user['sub'] ?? 'N/A') ?></code></td>
                                </tr>
                            </tbody>
                        </table>
                        
                        <h6 class="mt-4">账户状态</h6>
                        <div class="row">
                            <div class="col-sm-6">
                                <div class="card bg-light">
                                    <div class="card-body text-center">
                                        <i class="fas fa-shield-alt fa-2x text-success mb-2"></i>
                                        <h6>账户状态</h6>
                                        <span class="badge bg-success">活跃</span>
                                    </div>
                                </div>
                            </div>
                            <div class="col-sm-6">
                                <div class="card bg-light">
                                    <div class="card-body text-center">
                                        <i class="fas fa-clock fa-2x text-info mb-2"></i>
                                        <h6>最后登录</h6>
                                        <small class="text-muted">刚刚</small>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                
                <hr>
                
                <div class="d-grid gap-2 d-md-flex justify-content-md-end">
                    <a href="http://keycloak.localhost/realms/php-demo/account" 
                       target="_blank" 
                       class="btn btn-outline-primary">
                        <i class="fas fa-edit me-2"></i>
                        编辑资料
                    </a>
                    <a href="/logout" class="btn btn-outline-danger">
                        <i class="fas fa-sign-out-alt me-2"></i>
                        退出登录
                    </a>
                </div>
            </div>
        </div>
        
        <!-- 调试信息（开发环境） -->
        <?php if ($_ENV['APP_DEBUG'] ?? false): ?>
            <div class="card mt-4">
                <div class="card-header">
                    <h6 class="mb-0">
                        <i class="fas fa-bug me-2"></i>
                        调试信息
                    </h6>
                </div>
                <div class="card-body">
                    <pre class="bg-light p-3 rounded"><code><?= htmlspecialchars(json_encode($user, JSON_PRETTY_PRINT | JSON_UNESCAPED_UNICODE)) ?></code></pre>
                </div>
            </div>
        <?php endif; ?>
    </div>
</div>

<?php
$content = ob_get_clean();
include __DIR__ . '/layout.php';
?>
