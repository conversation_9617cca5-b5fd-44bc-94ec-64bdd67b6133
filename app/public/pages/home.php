<?php
$title = '首页 - PHP Demo App';

ob_start();
?>

<div class="row">
    <div class="col-lg-8 mx-auto">
        <!-- 欢迎卡片 -->
        <div class="card shadow-sm mb-4">
            <div class="card-body text-center">
                <h1 class="card-title">
                    <i class="fas fa-rocket text-primary me-2"></i>
                    欢迎使用 PHP Demo App
                </h1>
                <p class="card-text lead">
                    这是一个集成了 Keycloak 身份认证和 Traefik 反向代理的完整演示应用
                </p>
                
                <?php if (!$session->isAuthenticated()): ?>
                    <div class="d-grid gap-2 d-md-block">
                        <a href="/login" class="btn btn-primary btn-lg">
                            <i class="fas fa-sign-in-alt me-2"></i>
                            立即登录
                        </a>
                        <a href="/register" class="btn btn-outline-primary btn-lg">
                            <i class="fas fa-user-plus me-2"></i>
                            注册账户
                        </a>
                    </div>
                <?php else: ?>
                    <?php $user = $session->getUser(); ?>
                    <div class="alert alert-success" role="alert">
                        <h4 class="alert-heading">
                            <i class="fas fa-check-circle me-2"></i>
                            欢迎回来！
                        </h4>
                        <p>你好，<strong><?= htmlspecialchars($user['name'] ?? $user['preferred_username'] ?? '用户') ?></strong>！</p>
                        <hr>
                        <p class="mb-0">
                            <a href="/profile" class="btn btn-success me-2">查看个人资料</a>
                            <a href="/admin" class="btn btn-info">管理面板</a>
                        </p>
                    </div>
                <?php endif; ?>
            </div>
        </div>

        <!-- 功能特性 -->
        <div class="row">
            <div class="col-md-4 mb-4">
                <div class="card h-100">
                    <div class="card-body text-center">
                        <i class="fas fa-shield-alt fa-3x text-primary mb-3"></i>
                        <h5 class="card-title">安全认证</h5>
                        <p class="card-text">使用 Keycloak 提供企业级身份认证和授权管理</p>
                    </div>
                </div>
            </div>
            
            <div class="col-md-4 mb-4">
                <div class="card h-100">
                    <div class="card-body text-center">
                        <i class="fas fa-envelope fa-3x text-success mb-3"></i>
                        <h5 class="card-title">邮箱验证</h5>
                        <p class="card-text">支持邮箱注册和验证，确保用户身份真实性</p>
                    </div>
                </div>
            </div>
            
            <div class="col-md-4 mb-4">
                <div class="card h-100">
                    <div class="card-body text-center">
                        <i class="fas fa-users-cog fa-3x text-info mb-3"></i>
                        <h5 class="card-title">用户管理</h5>
                        <p class="card-text">完整的用户管理功能，包括角色和权限控制</p>
                    </div>
                </div>
            </div>
        </div>

        <!-- 系统信息 -->
        <div class="card">
            <div class="card-header">
                <h5 class="mb-0">
                    <i class="fas fa-info-circle me-2"></i>
                    系统信息
                </h5>
            </div>
            <div class="card-body">
                <div class="row">
                    <div class="col-md-6">
                        <h6>服务状态</h6>
                        <ul class="list-unstyled">
                            <li><i class="fas fa-check text-success me-2"></i>PHP 应用</li>
                            <li><i class="fas fa-check text-success me-2"></i>Traefik 代理</li>
                            <li><i class="fas fa-check text-success me-2"></i>Keycloak 认证</li>
                            <li><i class="fas fa-check text-success me-2"></i>MySQL 数据库</li>
                        </ul>
                    </div>
                    <div class="col-md-6">
                        <h6>快速链接</h6>
                        <ul class="list-unstyled">
                            <li><a href="http://keycloak.localhost" target="_blank">Keycloak 管理控制台</a></li>
                            <li><a href="http://traefik.localhost" target="_blank">Traefik 仪表板</a></li>
                            <li><a href="https://github.com" target="_blank">项目文档</a></li>
                        </ul>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<?php
$content = ob_get_clean();
include __DIR__ . '/layout.php';
?>
