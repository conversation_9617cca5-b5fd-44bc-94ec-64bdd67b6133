<?php
$title = '登录回调 - PHP Demo App';

// 检查授权码和状态参数
$code = $_GET['code'] ?? null;
$state = $_GET['state'] ?? null;
$error = $_GET['error'] ?? null;

if ($error) {
    $errorMessage = '登录失败：' . htmlspecialchars($_GET['error_description'] ?? $error);
} elseif (!$code) {
    $errorMessage = '缺少授权码';
} elseif (!$state || $state !== $session->get('oauth_state')) {
    $errorMessage = '无效的状态参数，可能存在 CSRF 攻击';
} else {
    // 清除状态参数
    $session->remove('oauth_state');
    
    // 使用授权码获取访问令牌
    $tokens = $keycloak->getAccessToken($code);

    if ($tokens && isset($tokens['access_token'])) {
        // 保存令牌
        $session->setTokens($tokens);

        // 从访问令牌中提取用户信息
        $userInfo = $keycloak->verifyToken($tokens['access_token']);

        if ($userInfo) {
            // 格式化用户信息以匹配预期的结构
            $formattedUserInfo = [
                'sub' => $userInfo['sub'] ?? '',
                'name' => $userInfo['name'] ?? '',
                'preferred_username' => $userInfo['preferred_username'] ?? '',
                'email' => $userInfo['email'] ?? '',
                'given_name' => $userInfo['given_name'] ?? '',
                'family_name' => $userInfo['family_name'] ?? '',
                'email_verified' => $userInfo['email_verified'] ?? false,
            ];

            // 保存用户信息到会话
            $session->setUser($formattedUserInfo);

            // 重定向到首页
            header('Location: /');
            exit;
        } else {
            $errorMessage = '无法解析访问令牌';
        }
    } else {
        $errorMessage = '无法获取访问令牌';
    }
}

ob_start();
?>

<div class="row justify-content-center">
    <div class="col-md-6">
        <?php if (isset($errorMessage)): ?>
            <div class="card">
                <div class="card-header bg-danger text-white">
                    <h5 class="mb-0">
                        <i class="fas fa-exclamation-triangle me-2"></i>
                        登录失败
                    </h5>
                </div>
                <div class="card-body">
                    <p class="card-text"><?= htmlspecialchars($errorMessage) ?></p>
                    <div class="d-grid gap-2">
                        <a href="/login" class="btn btn-primary">
                            <i class="fas fa-redo me-2"></i>
                            重新登录
                        </a>
                        <a href="/" class="btn btn-outline-secondary">
                            <i class="fas fa-home me-2"></i>
                            返回首页
                        </a>
                    </div>
                </div>
            </div>
        <?php else: ?>
            <div class="card">
                <div class="card-body text-center">
                    <div class="spinner-border text-primary mb-3" role="status">
                        <span class="visually-hidden">处理中...</span>
                    </div>
                    <h5>正在处理登录...</h5>
                    <p class="text-muted">请稍候，我们正在验证您的身份。</p>
                </div>
            </div>
        <?php endif; ?>
    </div>
</div>

<?php
$content = ob_get_clean();
include __DIR__ . '/layout.php';
?>
