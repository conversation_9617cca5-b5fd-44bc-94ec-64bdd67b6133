<?php

require_once __DIR__ . '/../vendor/autoload.php';

use App\KeycloakClient;
use App\Session;
use Dotenv\Dotenv;

// 加载环境变量
$dotenv = Dotenv::createImmutable(__DIR__ . '/..');
$dotenv->load();

// 加载配置
$keycloakConfig = require __DIR__ . '/../config/keycloak.php';

// 初始化服务
$session = new Session();
$keycloak = new KeycloakClient($keycloakConfig);

// 路由处理
$requestUri = $_SERVER['REQUEST_URI'];
$path = parse_url($requestUri, PHP_URL_PATH);

switch ($path) {
    case '/':
        include __DIR__ . '/pages/home.php';
        break;
    
    case '/login':
        // 生成状态参数防止 CSRF
        $state = bin2hex(random_bytes(16));
        $session->set('oauth_state', $state);
        
        // 重定向到 Keycloak 登录页面
        $authUrl = $keycloak->getAuthorizationUrl($state);
        header('Location: ' . $authUrl);
        exit;
    
    case '/callback':
        include __DIR__ . '/pages/callback.php';
        break;
    
    case '/logout':
        $idToken = $session->getIdToken();
        $session->logout();
        
        // 重定向到 Keycloak 注销页面
        $logoutUrl = $keycloak->getLogoutUrl($idToken);
        header('Location: ' . $logoutUrl);
        exit;
    
    case '/profile':
        include __DIR__ . '/pages/profile.php';
        break;
    
    case '/admin':
        include __DIR__ . '/pages/admin.php';
        break;
    
    case '/register':
        // 重定向到 Keycloak 注册页面
        $authUrl = $keycloak->getAuthorizationUrl() . '&kc_action=REGISTER';
        header('Location: ' . $authUrl);
        exit;
    
    default:
        http_response_code(404);
        include __DIR__ . '/pages/404.php';
        break;
}
