<?php

return [
    'base_url' => $_ENV['K<PERSON><PERSON><PERSON>OAK_BASE_URL'] ?? 'http://keycloak.localhost',
    'realm' => $_ENV['KEYCLOAK_REALM'] ?? 'php-demo',
    'client_id' => $_ENV['KEYCLOAK_CLIENT_ID'] ?? 'php-app',
    'client_secret' => $_ENV['KEYCLOAK_CLIENT_SECRET'] ?? 'php-app-secret-key-123',
    'redirect_uri' => ($_ENV['APP_URL'] ?? 'http://app.localhost') . '/callback',
    'logout_redirect_uri' => ($_ENV['APP_URL'] ?? 'http://app.localhost') . '/',
    
    // OpenID Connect 端点
    'endpoints' => [
        'authorization' => '/realms/{realm}/protocol/openid-connect/auth',
        'token' => '/realms/{realm}/protocol/openid-connect/token',
        'userinfo' => '/realms/{realm}/protocol/openid-connect/userinfo',
        'logout' => '/realms/{realm}/protocol/openid-connect/logout',
        'jwks' => '/realms/{realm}/protocol/openid-connect/certs',
    ]
];
