<?php

namespace App;

class Session
{
    public function __construct()
    {
        if (session_status() === PHP_SESSION_NONE) {
            session_start();
        }
    }

    public function set(string $key, $value): void
    {
        $_SESSION[$key] = $value;
    }

    public function get(string $key, $default = null)
    {
        return $_SESSION[$key] ?? $default;
    }

    public function has(string $key): bool
    {
        return isset($_SESSION[$key]);
    }

    public function remove(string $key): void
    {
        unset($_SESSION[$key]);
    }

    public function clear(): void
    {
        session_destroy();
        session_start();
    }

    public function setUser(array $user): void
    {
        $this->set('user', $user);
        $this->set('authenticated', true);
    }

    public function getUser(): ?array
    {
        return $this->get('user');
    }

    public function isAuthenticated(): bool
    {
        return $this->get('authenticated', false) && $this->has('user');
    }

    public function logout(): void
    {
        $this->remove('user');
        $this->remove('authenticated');
        $this->remove('access_token');
        $this->remove('id_token');
    }

    public function setTokens(array $tokens): void
    {
        if (isset($tokens['access_token'])) {
            $this->set('access_token', $tokens['access_token']);
        }
        if (isset($tokens['id_token'])) {
            $this->set('id_token', $tokens['id_token']);
        }
        if (isset($tokens['refresh_token'])) {
            $this->set('refresh_token', $tokens['refresh_token']);
        }
    }

    public function getAccessToken(): ?string
    {
        return $this->get('access_token');
    }

    public function getIdToken(): ?string
    {
        return $this->get('id_token');
    }
}
