<?php

namespace App;

use GuzzleHttp\Client;
use GuzzleHttp\Exception\RequestException;
use Firebase\JWT\JWT;
use Firebase\JWT\Key;

class KeycloakClient
{
    private $config;
    private $httpClient;

    public function __construct(array $config)
    {
        $this->config = $config;
        $this->httpClient = new Client([
            'base_uri' => $config['base_url'],
            'timeout' => 30,
        ]);
    }

    /**
     * 获取授权 URL
     */
    public function getAuthorizationUrl(string $state = null): string
    {
        $params = [
            'client_id' => $this->config['client_id'],
            'redirect_uri' => $this->config['redirect_uri'],
            'response_type' => 'code',
            'scope' => 'openid profile email',
        ];

        if ($state) {
            $params['state'] = $state;
        }

        $endpoint = str_replace('{realm}', $this->config['realm'], $this->config['endpoints']['authorization']);
        
        return $this->config['base_url'] . $endpoint . '?' . http_build_query($params);
    }

    /**
     * 通过授权码获取访问令牌
     */
    public function getAccessToken(string $code): ?array
    {
        try {
            $endpoint = str_replace('{realm}', $this->config['realm'], $this->config['endpoints']['token']);
            
            $response = $this->httpClient->post($endpoint, [
                'form_params' => [
                    'grant_type' => 'authorization_code',
                    'client_id' => $this->config['client_id'],
                    'client_secret' => $this->config['client_secret'],
                    'code' => $code,
                    'redirect_uri' => $this->config['redirect_uri'],
                ]
            ]);

            return json_decode($response->getBody()->getContents(), true);
        } catch (RequestException $e) {
            error_log('Keycloak token request failed: ' . $e->getMessage());
            return null;
        }
    }

    /**
     * 获取用户信息
     */
    public function getUserInfo(string $accessToken): ?array
    {
        try {
            $endpoint = str_replace('{realm}', $this->config['realm'], $this->config['endpoints']['userinfo']);
            
            $response = $this->httpClient->get($endpoint, [
                'headers' => [
                    'Authorization' => 'Bearer ' . $accessToken,
                ]
            ]);

            return json_decode($response->getBody()->getContents(), true);
        } catch (RequestException $e) {
            error_log('Keycloak userinfo request failed: ' . $e->getMessage());
            return null;
        }
    }

    /**
     * 获取注销 URL
     */
    public function getLogoutUrl(string $idToken = null): string
    {
        $params = [
            'client_id' => $this->config['client_id'],
            'post_logout_redirect_uri' => $this->config['logout_redirect_uri'],
        ];

        if ($idToken) {
            $params['id_token_hint'] = $idToken;
        }

        $endpoint = str_replace('{realm}', $this->config['realm'], $this->config['endpoints']['logout']);
        
        return $this->config['base_url'] . $endpoint . '?' . http_build_query($params);
    }

    /**
     * 验证 JWT 令牌
     */
    public function verifyToken(string $token): ?array
    {
        try {
            // 这里简化处理，生产环境应该验证 JWT 签名
            $parts = explode('.', $token);
            if (count($parts) !== 3) {
                return null;
            }

            $payload = json_decode(base64_decode($parts[1]), true);
            
            // 检查令牌是否过期
            if (isset($payload['exp']) && $payload['exp'] < time()) {
                return null;
            }

            return $payload;
        } catch (\Exception $e) {
            error_log('Token verification failed: ' . $e->getMessage());
            return null;
        }
    }
}
